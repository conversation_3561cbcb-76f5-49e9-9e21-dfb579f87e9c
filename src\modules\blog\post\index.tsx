import Image from 'next/image'
import PostBG from '/public/images/post-bg.jpg'
import { IArticle } from '@/types'

const PostPage = ({ title, date, image, body, media_type = 'image', link }: IArticle) => {
  return (
    <div className="container">
      <h2 className="text-primary-03 text-[28px] font-black text-start mb-12">{title}</h2>
      {media_type === 'image' ? (
        <div className="relative w-full h-[400px]">
          <Image src={image} alt="Article Image" fill className="rounded-[24px] object-cover" />
        </div>
      ) : (
        <div className="w-full aspect-video h-[400px]">
          <iframe
            width="100%"
            height="400"
            src={`https://www.youtube.com/embed/${link?.split('v=')[1]?.split('&')[0]}`}
            title="YouTube video player"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="rounded-[24px] overflow-hidden"
          />
        </div>
      )}
      <div className="flex justify-between items-center flex-wrap mt-6">
        <span className="text-gray-01 text-xl font-bold">{date}</span>
        <span>social shared</span>
      </div>
      <div className="mt-8">
        <p>{body}</p>
      </div>
    </div>
  )
}

export default PostPage
