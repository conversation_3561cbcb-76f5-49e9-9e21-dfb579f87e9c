'use client'
import { actionService, ActionServiceProps } from '@/services/actionService'
import { ActionServiceReturn } from '@/types'
import { startTransition, useActionState, useEffect } from 'react'
import useError from './useError'

interface Props extends ActionServiceProps {
  handleError?: boolean
  handleSuccess?: boolean
  onSuccess?: (res: any) => void
  onError?: (error: any) => void
  intermediate?: boolean
}

interface ApiReturn<T> {
  state: ActionServiceReturn<T>
  submit: (payload?: any) => void
  isPending: boolean
}

const useApi = <T>({
  handleError = true,
  handleSuccess = true,
  onSuccess,
  onError,
  intermediate,
  ...props
}: Props): ApiReturn<T> => {

  const { handleErrors } = useError()
  const serverAction = actionService.bind(null, props)
  const [state, action, isPending] = useActionState(serverAction, { status: false })
  const submit = (payload?: any) => {

    startTransition(() => {
      action(payload)
    })
  }

  useEffect(() => {
    if (intermediate) {
      submit()
    }
  }, [])

  useEffect(() => {
    if (state.status) {
      onSuccess && onSuccess(state.data)

      if (handleSuccess) {
        // if handleSuccess true handle success
      }
    } else if (state.error) {
      onError && onError(state)

      if (handleError) {
        // if handleError true use useError
        handleErrors(state.error)
      }
    }
  }, [state])

  return { state: state as ActionServiceReturn<T>, submit, isPending }
}

export default useApi
