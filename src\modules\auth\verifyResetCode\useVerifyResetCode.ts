import { getCookie, set<PERSON><PERSON><PERSON> } from 'cookies-next'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'
import { useEffect, useRef, useTransition } from 'react'

import { IVerifyResetCode } from '@/types'
import { IFormWrapper } from '@/components/core/FormWrapper'
import { toast } from 'sonner'
import useApi from '@/hooks/useApi'
import { Routes } from '@/routes/routes'
import useError from '@/hooks/useError'

const defaultValues: IVerifyResetCode = {
  email: '',
  code: '',
}

const useVerifyResetCode = () => {
  const t = useTranslations()
  const email = getCookie('em')
  const formRef = useRef<IFormWrapper>(null)
  const { errorToast } = useError()
  const [isPending, startTransition] = useTransition()
  const { action, state } = useApi({
    path: 'auth/verify-reset-code',
    method: 'POST',
    onSuccess: (state) => {
      toast.success(state.message)
      setCookie('code', state.code)
      redirect(Routes.AUTH.RESET_PASSWORD)
    },
    onError: (error) => {
      errorToast(error)
    },
  })

  const { action: resendAction } = useApi({
    path: 'auth/forgot-password',
    method: 'POST',
    onSuccess: (state) => {
      toast.success(state.message || t('auth.code_resent'))
    },
    onError: (error) => {
      toast.error(error || t('auth.validation_failed'))
    },
  })

  const onSubmit = async (payload: IVerifyResetCode) => {
    payload.email = email ?? ''
    payload.code = formRef.current?.getValues('code') ?? ''
    startTransition(() => {
      action(payload)
    })
  }

  const handleResendCode = async () => {
    startTransition(() => {
      resendAction({
        email: email ?? '',
      })
    })
  }

  useEffect(() => {
    const handlePopState = () => {
      sessionStorage.removeItem('counter')
    }

    window.addEventListener('popstate', handlePopState)
  }, [])

  return {
    t,
    email,
    isPending,
    onSubmit,
    defaultValues,
    handleResendCode,
    formRef,
  }
}

export default useVerifyResetCode
