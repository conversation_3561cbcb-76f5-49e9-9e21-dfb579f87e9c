import { getCookie, set<PERSON><PERSON><PERSON> } from 'cookies-next'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'

import { IVerifyResetCode } from '@/types'
import { IFormWrapper } from '@/components/core/FormWrapper'
import { toast } from 'sonner'
import useApi from '@/hooks/useApi'
import { Routes } from '@/routes/routes'

const defaultValues: IVerifyResetCode = {
  email: '',
  code: '',
}

const useVerifyResetCode = () => {
  const t = useTranslations()
  const email = getCookie('em')
  const formRef = useRef<IFormWrapper>(null)
  const [isAllowed, setIsAllowed] = useState(false)

  const { submit: allowedToSendResetCode } = useApi({
    path: '/auth/allowed-to-send-reset-code',
    method: 'POST',
    handleSuccess: false,
    onSuccess: (state) => {
      setIsAllowed(state.is_allowed)
    },
  })

  const { submit, isPending } = useApi({
    path: '/auth/check-reset-code',
    method: 'POST',
    handleSuccess: false,
    onSuccess: () => {
      toast.success(t('auth.code_verified_successfully'))
      redirect(Routes.AUTH.RESET_PASSWORD)
    },
  })

  const { submit: resendAction } = useApi({
    path: '/auth/forgot-password',
    method: 'POST',
    onSuccess: (state) => {
      toast.success(state.message || t('auth.code_resent'))
    },
  })

  const onSubmit = async (payload: IVerifyResetCode) => {
    payload.email = email ?? ''
    payload.code = formRef.current?.getValues('code') ?? ''
    setCookie('code', payload.code)
    const formdata = new FormData()
    formdata.append('email', payload.email)
    formdata.append('code', payload.code)
    submit(formdata)
  }

  const handleResendCode = async () => {
    const formdata = new FormData()
    formdata.append('email', email ?? '')
    resendAction(formdata)
    allowedToSendResetCode(formdata)
  }

  useEffect(() => {
    const formdata = new FormData()
    formdata.append('email', email ?? '')

    allowedToSendResetCode(formdata)
  }, [])

  useEffect(() => {
    const handlePopState = () => {
      sessionStorage.removeItem('counter')
    }

    window.addEventListener('popstate', handlePopState)
  }, [])

  return {
    t,
    email,
    isPending,
    onSubmit,
    defaultValues,
    handleResendCode,
    formRef,
    isAllowed,
  }
}

export default useVerifyResetCode
