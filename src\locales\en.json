{"name": "Prof. Dr. <PERSON>", "nav": {"email": "Email", "phone": "Phone", "home": "Home", "drAhmed": "Prof. Dr. <PERSON>", "articles": "Articles", "activities": "Activities", "faqs": "FAQs", "contact": "Contact", "bookings": "Bookings", "profile": "Profile", "logout": "Logout", "terms_and_conditions": "Terms and Conditions", "faq": "FAQs", "privacy_policy": "Privacy Policy", "help_center": "Help Center", "notifications": "Notifications", "language": "Language", "arabic": "Arabic", "english": "English"}, "footer": {"copyright": "All rights reserved ©", "top_text": "If you're looking for accurate diagnosis and specialized treatment for electrical heart problems", "top_text_2": "Book now and take the first step towards a stable and regular heartbeat", "working_schedules": "Working Schedules", "quick_links": "Quick Links", "other_links": "Other Links", "contact_info": "Contact Info"}, "button": {"book_now": "Book Now"}, "staticts": {"patients_rating": "Patients Rating", "years_of_experience": "Years of Experience", "treated_patients": "Treated Patients", "surgeries_performed": "Surgeries Performed"}, "dialog": {"confirm": "Confirm", "cancel": "Cancel"}, "label": {"name": "Name", "select_day": "Select day", "birth_date": "Birth date", "full_name": "Full Name", "reset_password": "Reset Password ?", "confirm_new_password": "Confirm New Password", "image": "Image", "logout": "Logout", "notification_title": "Notification title", "notification_body": "Notification body", "notifications_resent_successfully": "!Successfulluly, Notification has been resent", "date": "Date", "no_address": "No Address", "no_description": "No Description", "phone": "Phone number", "address": "Address", "search": "Search", "code": "Code", "password": "Password", "enter_password": "Enter Password", "email": "E-mail", "enter_email": "Enter your email address", "new_password": "New password", "enter_new_password": "Enter the new password ", "enter_new_password_confirmation": " Re-enter the new password", "new_password_confirmation": "Confirm the new password", "password_confirmation": "Confirm Password", "first_name": "First Name", "last_name": "Last Name", "confirm_password": "Confirm Password", "mobile_number": "Mobile Number", "username": "Username", "location": "Location"}, "table": {"no_results": "No Results"}, "languages": {"ar": "Arabic", "en": "English"}, "articles": {"read_more": "Read More"}, "status": {"success": "Success", "failed": "Failed", "disabled": "Disabled", "warning": "Warning"}, "auth": {"register_now_with_us": "Register now for free", "enter_to_create_account": "Enter to create a new account to continue", "create_new_account": "Create a new account", "already_have_account": "Already have an account?", "login": "Log in", "login_to_account": "Log in to your account", "welcome_back": "Welcome back, enter to continue and explore", "forgot_password": "Forgot password?", "no_account_yet": "Don't have an account yet?", "register_now": "Register now", "enter_email_reset": "Please enter the email you use to reset your password", "enter_code_email": "Please enter the code we sent by email to", "enter_new_password": "Please enter the new password", "verification_code": "Verification Code", "send": "Send", "resend_code": "Resend Code", "verify": "Verify", "back_to_login": "Back to login", "save": "Save", "saving": "Saving...", "loading": "Loading...", "reset_password": "Reset Password", "new_password": "New password", "new_password_confirmation": "Confirm New Password", "session_expired": "Session expired. Please start the password reset process again.", "missing_required_data": "Missing required data. Please try again.", "password_reset_success": "Password reset successfully! You can now log in with your new password.", "password_reset_failed": "Failed to reset password. Please try again.", "reset_code_sent": "Reset code has been sent to your email address.", "failed_to_send_code": "Failed to send reset code. Please try again.", "code_verified_successfully": "Code verified successfully!", "invalid_code": "Invalid or expired code. Please try again."}}