'use server'

import { signOut } from 'next-auth/react'
import { getServerAuthSession } from '@/config/auth'
import { env } from '@/config/environment'
import { getLocale } from 'next-intl/server'

export interface ApiServiceProps extends RequestInit {
  path: string
  searchParams?: Record<string, any>
  locale?: string
  revalidate?: number | false
  enabled?: boolean
}

export async function apiService({ path, searchParams, revalidate, enabled = true, ...props }: ApiServiceProps) {
  // Return early if disabled
  if (!enabled) {
    return { data: null }
  }

  try {
    const user = await getServerAuthSession()

    const locale = await getLocale()

    const headers = {
      'Accept-Language': locale || 'en',
      Accept: 'application/json',
      ...props.headers,
      ...(user?.user.token && { Authorization: `Bearer ${user?.user.token}` }),
    }

    const BASE_URL = env.BASE_API
    const urlSearchParams = new URLSearchParams(searchParams)
    const url = `${BASE_URL}${path}${!!urlSearchParams.size ? `?${urlSearchParams.toString()}` : ''}`

    const res = await fetch(url, {
      body: props.body,
      method: props.method || 'GET',
      headers,
      next: {
        tags: [path, urlSearchParams.toString() || ''],
        ...(revalidate !== undefined && { revalidate }),
        ...props.next,
      },
    })

    if (res.ok) {
      return res
    } else {
      let errorResponse = await res.json()

      if (res.status === 401) {
        return signOut({ callbackUrl: `/auth/login`, redirect: true })
      } else if (res.status === 422) {
        throw {
          status: 422,
          errors: errorResponse.errors || errorResponse.message || 'Validation failed',
          message: 'Validation failed',
        }
      } else {
        throw {
          status: res.status,
          message: errorResponse.message || res.statusText || 'Request failed',
          error: errorResponse,
        }
      }
    }
  } catch (e) {
    return e
  }
}
