'use client'

import { ReactNode } from 'react'

// utils
import { cn } from '@/utils/cn'

// components
import Image from 'next/image'

// types
import { IActivity } from '@/types/activities'

// icons
import { Calendar, Clock, MapPin } from 'lucide-react'
import { useTranslations } from 'next-intl'

interface IProps extends IActivity {
  className?: string
  index?: number // Add index prop for alternating logic
}

function ActivityItem({ className, index = 0, ...activity }: IProps) {
  const t = useTranslations()
  const isEven = index % 2 === 0
  const iconContainerClassName = cn(
    'flex justify-center items-center sm:p-1 rounded-full sm:size-16 w-fit p-2',
    !isEven ? 'bg-action-03-bg' : 'bg-action-02-bg'
  )

  const lineData = [
    {
      id: 1,
      icon: <Calendar className="size-5 -gray-01 stroke-gray-01" />,
      text: activity.date,
    },
    {
      id: 2,
      icon: <Clock className="size-5 -gray-01 stroke-gray-01" />,
      text: `${activity.from} - ${activity.to}`,
    },
    {
      id: 3,
      icon: <MapPin className="size-5 -gray-01 stroke-gray-01" />,
      text: activity.location,
    },
  ]

  return (
    <div className={cn('flex justify-start items-start gap-2 bg-white p-4 rounded-xl basic_card_shadow', className)}>
      <div className={iconContainerClassName}>
        {!isEven ? (
          <Image
          className="max-sm:w-[28px]"
            src={require('/public/icons/doctor_icon.svg')}
            alt={'Doctor Icon Profile icon'}
            width={32}
            height={32}
          />
        ) : (
          <Image
          className="max-sm:w-[28px]"
            src={require('/public/icons/doctor_stethoscope.svg')}
            alt={'Doctor Stethoscope icon'}
            width={32}
            height={32}
          />
        )}
      </div>
      {/* ************************************************ */}

      <div className="flex flex-col items-start justify-center gap-1 w-full">
        <div className="flex justify-between flex-wrap gap-2 items-center w-full">
          <p className="text-primary-03 lg:text-[28px] md:text-[24px] sm:text-[20px] text-[18px] font-normal">
            {activity.title}
          </p>

          {activity.status === 'upcoming' && (
            <div className="bg-action-04-bg text-action-04 lg:px-8 md:px-6 px-4 lg:py-3  py-2 rounded-3xl"> {t('status.upcoming')} </div>
          )}
        </div>
        <p className="text-gray-01 lg:text-[20px] md:text-[18px] sm:text-base text-sm font-bold">
          {activity.description}
        </p>

        <div className="flex justify-start items-center gap-3 flex-wrap">
          {lineData.map((item) => (
            <MultiTextAtLineItem key={item.id} text={item.text} icon={item.icon} />
          ))}
        </div>
      </div>
    </div>
  )
}

const MultiTextAtLineItem = ({ text, className, icon }: { text: string; className?: string; icon?: ReactNode }) => {
  return (
    <p
      className={cn(
        'text-primary-03 lg:text-[20px] md:text-[18px] sm:text-base text-sm font-normal flex justify-start items-center gap-0.5',
        className
      )}
    >
      {icon}
      {text}
    </p>
  )
}

export { ActivityItem }
