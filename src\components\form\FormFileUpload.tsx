'use client'

import { cn } from '@/lib/utils'
import { useState, useRef, type ChangeEvent } from 'react'
import { useTranslations } from 'next-intl'
import { useController, useFormContext } from 'react-hook-form'
import { useFormWrapperContext } from '../core/FormWrapper'
import { FormField, FormItem, FormLabel } from '../ui/form'
import { File } from 'lucide-react'
import { toast } from 'sonner'
import Image from 'next/image'
import ProfilePlaceHolder from '/public/images/profile-placeholder.png'
import EditIcon from '/public/icons/edit.svg'
import { IFileResponse } from '@/types'

type fileTypes =
  | 'image/*'
  | 'video/*'
  | 'audio/*'
  | 'application/pdf'
  | 'application/msword'
  | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  | 'application/vnd.ms-excel'
  | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

interface FilesPreviewProps {
  selectedFiles: (File | IFileResponse)[] | File | IFileResponse
  removeImage: (index: number, url?: string) => void
}

interface FormFileUploadProps {
  label?: string
  name: string
  maxSize?: number // in MB
  maxLength?: number
  accept?: fileTypes
  multiple?: boolean
  onFileSelect?: (file: File) => void
  isPending?: boolean
}

export function FormFileUpload({
  label,
  name,
  multiple,
  maxSize = 10,
  maxLength,
  accept = 'image/*',
  onFileSelect,
  isPending,
}: FormFileUploadProps) {
  const t = useTranslations()
  const { errors } = useFormWrapperContext()
  const { control, setValue, getValues, watch } = useFormContext()

  const [isDragging, setIsDragging] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const selectedFiles = watch(name) || []
  const {
    field: { onChange, value, ref },
  } = useController({ name, control })

  const handleFileSelect = (files: FileList) => {
    // Validate max file number
    if (multiple && !validateMaxLength(Array.from(files)))
      return toast.error(t('validations.too_many_files', { maxLength }))

    Array.from(files).forEach((file) => {
      if (!file) return

      // Validate file size
      if (!validateMaxSize(file)) return toast.error(t('validations.file_too_large', { maxSize }))

      setValue(name, multiple ? [...(getValues(name) || []), file] : file)
    })
  }

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files

    if (files) {
      handleFileSelect(files)
    }
  }

  const removeImage = (index: number, url?: string) => {
    if (url) URL.revokeObjectURL(url)

    if (fileInputRef.current) fileInputRef.current.value = ''

    // In case input not accepting multiple files
    if (!multiple) return setValue(name, null)

    // In case input accepting multiple files
    const filteredValues = Array.from(selectedFiles).filter((_, i) => i !== index)
    setValue(name, filteredValues)
  }

  const validateMaxLength = (files: File[]): boolean => {
    const currentFiles = getValues(name) || []
    const selectedFilesLength = Array.isArray(currentFiles) ? currentFiles.length : currentFiles ? 1 : 0
    if (maxLength && Array.from(files).length + selectedFilesLength > maxLength) return false

    return true
  }

  const validateMaxSize = (file: File) => {
    if (file.size > maxSize * 1024 * 1024) return false

    return true
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = e.dataTransfer.files
    if (files) {
      handleFileSelect(files)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      onChange(e.target.files) // يحافظ على القيمة في RHF
      onFileSelect?.(file) // ينادي على submit مباشرة
    }
  }

  return (
    <FormField
      name={name}
      control={control}
      render={() => (
        <label htmlFor={`image-upload-${name}`} className="block w-fit mx-auto">
          <FormItem className="w-fit">
            {label && <FormLabel>{label}</FormLabel>}
            <div
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              className={cn(
                'relative w-fit mx-auto mb-12 cursor-pointer',
                isDragging && 'border-primary',
                errors[name] && 'bg-red-50 border-red-300 hover:border-red-400'
              )}
            >
              <Image src={ProfilePlaceHolder} alt="Image" width={150} height={150} />
              <input
                multiple={multiple}
                ref={fileInputRef}
                id={`image-upload-${name}`}
                type="file"
                accept={accept}
                onChange={handleFileChange}
                className="hidden"
              />
              <FilesPreview selectedFiles={selectedFiles} removeImage={removeImage} />
              <Image src={EditIcon} alt="Image" width={32} height={32} className="absolute bottom-0 right-0" />
            </div>
          </FormItem>
        </label>
      )}
    />
  )
}

const FilesPreview = ({ selectedFiles }: FilesPreviewProps) => {
  if (!selectedFiles) return <></>

  const filesAsArray = Array.isArray(selectedFiles) ? selectedFiles : [selectedFiles]

  return (
    <div className="absolute top-0 left-[50%] translate-x-[-50%]">
      {filesAsArray.map((file, index) => {
        const isExistingFile = 'path' in file
        const fileUrl = isExistingFile ? file.path : URL.createObjectURL(file)

        return (
          <div
            key={isExistingFile ? `${file.id}-${file.name}` : file.name}
            className="transition-shadow shadow-xs bg-transparent dark:bg-input/30 rounded-full"
          >
            <div className="w-[150px] h-[150px] grid place-items-center relative overflow-hidden rounded-full">
              {file.type.startsWith('image/') ? (
                <img alt={file.name} src={fileUrl} className="object-cover object-center w-full h-full" />
              ) : (
                <File size={24} />
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}
