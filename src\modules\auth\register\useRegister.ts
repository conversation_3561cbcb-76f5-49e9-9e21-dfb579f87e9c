import useApi from '@/hooks/useApi'
import { IRegister } from '@/types'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'
import { redirect } from 'next/navigation'
import { Routes } from '@/routes/routes'
import { useEffect } from 'react'

const useRegister = () => {
  const t = useTranslations()

  const { submit, isPending, state } = useApi({
    path: '/auth/register',
    method: 'POST',
    handleSuccess: false,
    onSuccess: (state) => {
      toast.success(t('auth.register_success'))
      redirect(Routes.HOME)
    },
  })

  useEffect(() => {
    if (state) {
      console.log('form inside useEffect', state)
    }
  }, [state])

  const handleSubmit = (payload: IRegister) => {
    const formdata = new FormData()
    formdata.append('first_name', payload.first_name)
    formdata.append('last_name', payload.last_name)
    formdata.append('phone', payload.phone)
    formdata.append('email', payload.email)
    formdata.append('password', payload.password)
    formdata.append('password_confirmation', payload.password_confirmation)

    submit(formdata)
  }
  return {
    t,
    isPending,
    handleSubmit,
  }
}

export default useRegister
