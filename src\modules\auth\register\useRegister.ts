import useApi from '@/hooks/useApi'
import { IRegister } from '@/types'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'
import { redirect } from 'next/navigation'
import { Routes } from '@/routes/routes'
import useError from '@/hooks/useError'

const useRegister = () => {
  const t = useTranslations()
  const { errorToast } = useError()
  const { submit, isPending } = useApi({
    path: 'auth/register',
    method: 'POST',
    onSuccess: (state) => {
      console.log(state)
      toast.success(state.message)
      // redirect(Routes.HOME)
    },
    onError: (error) => {
      errorToast(error)
    },
  })

  const handleSubmit = (payload: IRegister) => {
    const formdata = new FormData()
    Object.keys(payload).forEach((key) => {
      formdata.append(key, payload[key as keyof IRegister])
    })
    submit(formdata)
  }
  return {
    t,
    isPending,
    handleSubmit,
  }
}

export default useRegister
