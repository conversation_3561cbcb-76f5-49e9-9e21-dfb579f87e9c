import FilterSelect from '@/components/filters/FilterSelect'
import SearchFilter from '@/components/filters/SearchFilter'
import { SearchIcon } from 'lucide-react'
import { useTranslations } from 'next-intl'

const BlogFilters = () => {
  const t = useTranslations()
  const orderOptions = [
    { label: t('label.all'), value: 'all' },
    { label: t('label.newest_to_oldest'), value: 'newest' },
    { label: t('label.oldest_to_newest'), value: 'oldest' },
  ]
  return (
    <div className="flex justify-between gap-4 w-full my-12 flex-wrap">
      <SearchFilter
        name="search"
        placeholder={t('label.search_here')}
        prefix={<SearchIcon color="#A7A9AC" size={16} />}
        className="bg-gray-03 rounded-[24px] w-full h-12 text-lg"
      />
      <FilterSelect
        name="order"
        label={t('label.order_by')}
        placeholder={t('label.select_order')}
        data={orderOptions}
        valueKey="value"
        labelKey="label"
      />
    </div>
  )
}

export default BlogFilters
