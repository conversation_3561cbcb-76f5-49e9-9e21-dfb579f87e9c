// Components
import { Footer } from '@/components/layouts/footer'
import ConditionalHeader from '@/components/layouts/ConditionalHeader'
import { apiService } from '@/services'

export default async function PrivateLayoutComponent({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const doctorInfo = await apiService({
    path: '/doctor-info',
    revalidate: 1800, // Cache for 24 hours - enables static generation
  })

  return (
    <main className="flex min-h-screen flex-col items-center justify-between">
      <ConditionalHeader doctorInfo={doctorInfo?.data} />

      {children}

      <Footer />
    </main>
  )
}
