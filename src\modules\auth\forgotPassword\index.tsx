'use client'

import Link from 'next/link'

import { forgotPasswordSchema } from '../schema'

import { FormWrapper } from '@/components/core/FormWrapper'
import { FormInput } from '@/components/form'
import { Button } from '@/components/ui/button'
import HeaderPage from '../components/HeaderPage'
import useForgetPassword from './useForgetPassword'

const ForgetPassword = () => {
  const { t, handleSubmit, defaultValues, isPending } = useForgetPassword()

  return (
    <>
      <HeaderPage title="forgot_password" description="enter_email_reset" />
      <FormWrapper defaultValues={defaultValues} onSubmit={handleSubmit} schema={forgotPasswordSchema}>
        <FormInput
          name="email"
          className="min-w-[300px] sm:min-w-[380px]"
          type="email"
          label={t('label.email')}
          placeholder={t('label.email')}
        />
        <Button className="block mx-auto mt-9 mb-3" type="submit" disabled={isPending}>
          {t('auth.send')}
        </Button>
        <Link className="text-primary-02 font-bold block w-full text-center" href={'/auth/login'}>
          {t('auth.back_to_login')}
        </Link>
      </FormWrapper>
    </>
  )
}

export default ForgetPassword
