'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import { Button } from '@/components/ui/button'
import { FormInput, FormTextArea } from '@/components/form'
import { profileSchema } from './schema'
import useProfileForm from './useProfileForm'

const ProfileForm = () => {
  const { t, isPending, handleSubmit, defaultValues } = useProfileForm()
  return (
    <FormWrapper schema={profileSchema} defaultValues={defaultValues} onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormInput
          name="first_name"
          className="min-w-[300px] sm:min-w-[380px]"
          label={t('label.full_name')}
          placeholder={t('label.last_name')}
        />
        <FormInput
          name="last_name"
          className="min-w-[300px] sm:min-w-[380px]"
          label={t('label.last_name')}
          placeholder={t('label.full_name')}
        />
        <FormInput
          name="email"
          className="min-w-[300px] sm:min-w-[380px]"
          type="email"
          label={t('label.email')}
          placeholder={t('label.email')}
        />
        <FormInput
          name="phone"
          className="min-w-[300px] sm:min-w-[380px]"
          type="number"
          label={t('label.phone')}
          placeholder={t('label.email')}
        />
      </div>
      <FormTextArea name="message" label={t('label.message')} placeholder={t('label.message')} />

      <div className="text-center">
        <Button className="mt-9 mb-3" type="submit" loading={isPending}>
          {t('button.send')}
        </Button>
      </div>
    </FormWrapper>
  )
}

export default ProfileForm
