'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import useRegister from './useRegister'
import { FormInput } from '@/components/form'
import { FormPasswordInput } from '@/components/form/FormPasswordInput'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { registerSchema } from '../schema'
import HeaderPage from '../components/HeaderPage'
import { Routes } from '@/routes/routes'

const defaultValues = {
  first_name: '',
  last_name: '',
  phone: '',
  email: '',
  password: '',
  password_confirmation: '',
}

const Register = () => {
  const { t, handleSubmit, isPending } = useRegister()
  return (
    <>
      <HeaderPage title="register_now_with_us" description="enter_to_create_account" />
      <FormWrapper schema={registerSchema} defaultValues={defaultValues} onSubmit={handleSubmit}>
        <div className="grid md:grid-cols-2 gap-2">
          <FormInput
            containerClassName="flex"
            name="first_name"
            label={t('label.first_name')}
            placeholder={t('label.first_name')}
            className="min-w-[320px] md:min-w-[160px]"
          />
          <FormInput
            containerClassName="flex"
            name="last_name"
            label={t('label.last_name')}
            placeholder={t('label.last_name')}
            className="sm:min-w-[160px]"
          />
        </div>
        <FormInput name="email" type="email" label={t('label.email')} placeholder={t('label.email')} />
        <FormInput name="phone" type="number" label={t('label.phone')} placeholder={t('label.phone')} />
        <FormPasswordInput name="password" label={t('label.password')} placeholder={t('label.password')} />
        <FormPasswordInput
          name="password_confirmation"
          label={t('label.password_confirmation')}
          placeholder={t('label.password_confirmation')}
        />
        <div className="text-center">
          <Button className="mt-9 mb-3" type="submit" disabled={isPending}>
            {t('auth.create_new_account')}
          </Button>
          <p className="text-primary-02 font-bold">
            {t('auth.already_have_account')}
            <Link className="text-primary-01" href={Routes.AUTH.LOGIN}>
              {t('auth.login')}
            </Link>
          </p>
        </div>
      </FormWrapper>
    </>
  )
}

export default Register
