import { FormWrapper } from '@/components/core/FormWrapper'
import { FormFileUpload } from '@/components/form/FormFileUpload'
import useApi from '@/hooks/useApi'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'
import { object, string } from 'yup'

const profileSchema = object({
  image: string().required(),
})

const defaultValues = {
  image: null,
}

const ProfileImage = () => {
  const t = useTranslations()
  const { submit, isPending } = useApi({
    path: '/profile/update-image',
    method: 'POST',
    handleSuccess: false,
    onSuccess: () => {
      toast.success(t('contact.send_message_success'))
    },
  })
  const handleSubmit = (payload: any) => {
    const formData = new FormData()
    formData.append('image', payload.image[0])
    submit(formData)
  }
  return (
    <FormWrapper schema={profileSchema} defaultValues={defaultValues} onSubmit={handleSubmit}>
      <FormFileUpload name="image" onFileSelect={handleSubmit} isPending={isPending} />
    </FormWrapper>
  )
}

export default ProfileImage
