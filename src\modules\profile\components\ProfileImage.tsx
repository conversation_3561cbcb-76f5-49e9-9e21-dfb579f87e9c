'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import { FormFileUpload } from '@/components/form/FormFileUpload'
import useApi from '@/hooks/useApi'

import { toast } from 'sonner'
import { object, mixed } from 'yup'
import { useEffect } from 'react'

const profileSchema = object({
  image: mixed().required('يرجى اختيار صورة'),
})

const defaultValues = {
  image: null,
}

const ProfileImage = () => {
  const { submit, isPending } = useApi({
    path: '/profile/update-image',
    method: 'POST',
    handleSuccess: false,
    onSuccess: () => {
      toast.success('تم تحديث الصورة الشخصية بنجاح')
    },
    onError: () => {
      toast.error('حدث خطأ أثناء تحديث الصورة الشخصية')
    },
  })

  const handleSubmit = (payload: any) => {
    console.log('test form yajhay')
    // if (!payload.image) {
    //   toast.error('يرجى اختيار صورة أولاً')
    //   return
    // }

    // const formData = new FormData()
    // formData.append('image', payload.image)
    // submit(formData)
  }

  return (
    <div className="relative">
      <FormWrapper schema={profileSchema} defaultValues={defaultValues} onSubmit={handleSubmit} autoSubmit={true}>
        <FormFileUpload name="image" />
      </FormWrapper>
      {isPending && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-full">
          <div className="bg-white px-4 py-2 rounded-lg shadow-lg">
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
              <span className="text-sm font-medium">جاري رفع الصورة...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ProfileImage
