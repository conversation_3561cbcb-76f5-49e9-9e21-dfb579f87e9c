import { ILogin } from '@/types'
import { signIn } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'
import { useState } from 'react'

import { toast } from 'sonner'

const useLogin = () => {
  const t = useTranslations()
  const [isPending, setIsPending] = useState(false)

  const handleSubmit = async (payload: ILogin) => {
    const formdata = new FormData()
    formdata.append('email', payload.email)
    formdata.append('password', payload.password)

    let success = false
    try {
      setIsPending(true)
      const result = await signIn('credentials', { formdata, redirect: false } as any)

      if (!result?.ok) {
        const serverError = JSON.parse(result?.error ?? '')
        if (serverError?.data && typeof serverError?.data === 'string') {
          toast.error(serverError?.data)
        }
      }

      success = true
    } catch (error) {
      console.error('🚀 ~ onSubmit ~ error:', error)
    } finally {
      setIsPending(false)
    }

    if (success) redirect('/')
  }

  return {
    t,
    isPending,
    handleSubmit,
  }
}

export default useLogin
