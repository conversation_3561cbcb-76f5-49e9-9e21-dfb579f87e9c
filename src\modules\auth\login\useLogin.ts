import { ILogin } from '@/types'
import { signIn } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'
import { useState } from 'react'

import { toast } from 'sonner'

const useLogin = () => {
  const t = useTranslations()
  const [isPending, setIsPending] = useState(false)

  const handleSubmit = async (payload: ILogin) => {
    const formdata = new FormData()
    formdata.append('email', payload.email)
    formdata.append('password', payload.password)

    let success = false
    try {
      setIsPending(true)
      const result = await signIn('credentials', { formdata, redirect: false } as any)

      if (!result?.ok) {
        let errorMessage = 'Authentication failed'

        if (result?.error) {
          // Handle NextAuth error types
          if (result.error === 'CredentialsSignin') {
            errorMessage = 'Invalid email or password'
          } else {
            // Try to parse as JSON in case it's a server error
            try {
              const serverError = JSON.parse(result.error)
              if (typeof serverError === 'string') {
                errorMessage = serverError
              } else if (Array.isArray(serverError)) {
                // Handle 422 validation errors (array format)
                errorMessage = serverError.join(', ')
              } else if (serverError?.message) {
                errorMessage = serverError.message
              } else if (serverError?.data && typeof serverError.data === 'string') {
                errorMessage = serverError.data
              }
            } catch (parseError) {
              // If JSON parsing fails, use the error string as-is
              errorMessage = result.error
            }
          }
        }

        toast.error(errorMessage)
      }

      success = true
    } catch (error) {
      console.error('🚀 ~ onSubmit ~ error:', error)
    } finally {
      setIsPending(false)
    }

    if (success) redirect('/')
  }

  return {
    t,
    isPending,
    handleSubmit,
  }
}

export default useLogin
