'use client'

import { resetPasswordSchema } from '../schema'
import useResetPassword from './useResetPassword'
import HeaderPage from '../components/HeaderPage'
import { Button } from '@/components/ui/button'
import { FormWrapper } from '@/components/core/FormWrapper'
import { FormPasswordInput } from '@/components/form/FormPasswordInput'

const ResetPassword = () => {
  const { t, onSubmit, defaultValues, isPending } = useResetPassword()

  return (
    <>
      <HeaderPage title="reset_password" description="enter_new_password" />
      <FormWrapper defaultValues={defaultValues} onSubmit={onSubmit} schema={resetPasswordSchema}>
        <div className="flex flex-col gap-4">
          <FormPasswordInput
            className="min-w-[300px] sm:min-w-[380px]"
            name="password"
            label={t('label.new_password')}
            placeholder={t('label.new_password')}
            disabled={isPending}
          />
          <FormPasswordInput
            name="password_confirmation"
            label={t('label.new_password_confirmation')}
            placeholder={t('label.new_password_confirmation')}
            disabled={isPending}
          />
        </div>
        <Button type="submit" className="block mt-9 mx-auto" disabled={isPending}>
          {isPending ? (
            <div className="flex items-center justify-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              {t('button.saving')}
            </div>
          ) : (
            t('button.save')
          )}
        </Button>
      </FormWrapper>
    </>
  )
}

export default ResetPassword
