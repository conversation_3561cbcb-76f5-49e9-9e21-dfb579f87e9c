import { Routes } from '@/routes/routes'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'
import Image from 'next/image'

// images
import LogoFooter from '/public/logo.webp'

// types
import { IDoctorInfo } from '@/types/doctorInfo'

// shared components
import { FooterLinksPreview } from '@/components/layouts/footer/components/footerLinksPreview'
import { WorkingSchedules } from '@/components/shared/workingSchedules'
import { DoctorContactLinks } from '@/components/shared/doctorContactLinks'

const FooterContent = async ({ doctorInfo }: { doctorInfo: IDoctorInfo }) => {
  const t = await getTranslations()

  const itemClassName = 'text-primary-03 text-lg hover:underline'

  const fastLinks = [
    {
      title: t('nav.home'),
      link: Routes.HOME,
    },
    {
      title: t('nav.drAhmed'),
      link: Routes.ABOUT,
    },
    {
      title: t('nav.articles'),
      link: Routes.ARTICLES,
    },
    {
      title: t('nav.activities'),
      link: Routes.ACTIVITIES,
    },
    {
      title: t('nav.bookings'),
      link: Routes.BOOKINGS,
    },
    {
      title: t('nav.faqs'),
      link: Routes.FAQS,
    },
    {
      title: t('about.qualifications'),
      link: Routes.QUALIFICATIONS,
    },
    {
      title: t('nav.contact'),
      link: Routes.CONTACT,
    },
  ]

  const otherLinks = [
    {
      title: t('nav.terms_and_conditions'),
      link: Routes.TERMS_AND_CONDITIONS,
    },
    {
      title: t('nav.privacy_policy'),
      link: Routes.PRIVACY_POLICY,
    },
    {
      title: t('nav.help_center'),
      link: Routes.HELP_CENTER,
    },
  ]

  return (
    <section className="container grid lg:grid-cols-6 sm:grid-cols-2 grid-cols-1 sm:gap-4 gap-6 border-b border_gradient lg:pb-8 pt-14 py-4">
      {/* First Column */}
      <div className="lg:col-span-2 col-span-1 flex flex-col lg:gap-6 md:gap-4 gap-3">
        <Link href={Routes.HOME} className="flex justify-start items-center lg:mb-4 md:mb-3 mb-2">
          <Image src={LogoFooter} alt="logo" width={100} height={92} />
        </Link>

        {doctorInfo?.schedule && doctorInfo.schedule.length > 0 && (
          <WorkingSchedules
            doctorSchedule={doctorInfo.schedule}
            title={t('footer.working_schedules')}
            className="lg:gap-6 md:gap-4 gap-3"
          />
        )}
      </div>

      <div className=" col-span-1 flex flex-col lg:gap-6 md:gap-4 gap-3">
        <FooterTitle title={t('footer.quick_links')} />
        <FooterLinksPreview links={fastLinks} className="lg:gap-6 md:gap-4 gap-3" itemsClassName={itemClassName} />
      </div>

      <div className=" col-span-1 flex flex-col lg:gap-6 md:gap-4 gap-3">
        <FooterTitle title={t('footer.other_links')} />
        <FooterLinksPreview links={otherLinks} itemsClassName={itemClassName} className="lg:gap-6 md:gap-4 gap-3" />
      </div>

      <DoctorContactLinks
        className="lg:col-span-2 col-span-1 lg:gap-6 md:gap-4 gap-3"
        {...doctorInfo}
        title={t('footer.contact_info')}
      />
    </section>
  )
}

const FooterTitle = ({ title }: { title: string }) => {
  return <h5 className="text-primary-01 font-bold md:text-lg text-base">{title}</h5>
}

export { FooterContent }
