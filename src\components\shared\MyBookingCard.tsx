import { useTranslations } from 'next-intl'
import { Button } from '../ui/button'
import { LocationIcon } from '../icons/social/LocationIcon'
import ClockIcon from '../icons/ClockIcon'
import CalenderIcon from '../icons/CalenderIcon'

export type TMyBookingCardStatus = 'upcoming' | 'completed' | 'cancelled'

export interface IMyBookingCard {
  id: number
  type: 'online' | 'offline'
  date: string
  from: string
  to: string
  rating: number
  status: TMyBookingCardStatus
  client_attended: boolean
}

const MyBookingCard = ({ type = 'online', date, from, to, rating, status, client_attended }: IMyBookingCard) => {
  const t = useTranslations()
  return (
    <div className="bg-primary-bg rounded-[24px] p-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-1">
          <h3 className="text-primary-02 text-[28px]">{t(`my_booking.${type}`)}</h3>
          <span className="text-gray-01 text-lg">{date}</span>
        </div>
        <p className="w-[112px] h-12 rounded-[24px] text-lg flex justify-center items-center">{status}</p>
      </div>
      <div className="text-primary-03 text-xl flex items-center gap-6">
        <span className="flex items-center gap-1">
          <CalenderIcon width={14} height={16} /> {from}
        </span>
        <span className="flex items-center gap-1">
          <ClockIcon width={16} height={16} /> {to}
        </span>
      </div>
      <div className="flex items-center gap-1 text-primary-03">
        <LocationIcon width={16} height={16} />
        <a
          href={'clinic'}
          target="_blank"
          rel="noopener noreferrer"
          className="text-primary-03 text-xl hover:underline"
        >
          clinic
        </a>
      </div>
      <p className="text-gray-01 text-xl">starts ({rating}/5)</p>
      <div className="flex gap-2">
        <Button>{t('button.examination_start')}</Button>
        <Button className="bg-action-07">{t('button.cancel_appointment')}</Button>
      </div>
    </div>
  )
}

export default MyBookingCard
