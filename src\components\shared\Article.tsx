import Image from 'next/image'
import Link from 'next/link'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { IArticle } from '@/types'
import { useTranslations } from 'next-intl'
import { Routes } from '@/routes/routes'
import ShareIcons from './ShareIcons'

const Article = ({ date, title, image, id }: IArticle) => {
  const t = useTranslations()
  const locale = useLocale()

  return (
    <div className="bg-white rounded-[24px] overflow-hidden basic_card_shadow">
      <div className="relative w-full h-[250px]">
        <Link href={Routes.ARTICLES.POST.replace(':id', id.toString())}>
          <div className="relative w-full h-full">
            <Image
              src={image || '/images/img.png'}
              alt="Article Image"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
        </Link>
      </div>
      <div className="p-4 md:p-8">
        <div className="flex justify-between items-center flex-wrap">
          <p className="text-xl font-bold text-gray-01">{date}</p>
          <ShareIcons url={`/article/${id}`} />
        </div>
        <h3 className="text-primary-03 text-[22px] md:text-[28px] py-4">{title}</h3>
        <Link
          href={Routes.ARTICLES.POST.replace(':id', id.toString())}
          className="text-primary-02 font-bold text-lg md:text-2xl flex items-center"
        >
          {locale === 'en' ? <ChevronRight /> : <ChevronLeft />}
          {t('articles.read_more')}
        </Link>
      </div>
    </div>
  )
}

export default Article
