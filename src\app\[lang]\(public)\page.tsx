import { apiService } from '@/services'
import { setRequestLocale } from 'next-intl/server'

// types
import { IPageProps } from '@/types'

// Components
import Home from '@/modules/home'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Home',
}

export default async function HomePage({ params }: IPageProps) {
  const resolvedParams = await params

  // Enable static rendering by setting the request locale
  setRequestLocale(resolvedParams.lang)

  const [home, doctorInfo] = await Promise.all([
    apiService({
      path: '/home',
      revalidate: 1800, // Cache for 24 hours - enables static generation
    }),
    apiService({
      path: '/doctor-info',
      revalidate: 1800, // Cache for 24 hours - enables static generation
    }),
  ])

  return <Home doctorInfo={doctorInfo?.data} home={home?.data} />
}
