import Image from 'next/image'
import Link from 'next/link'

const ShareIcons = ({ url }: { url: string }) => {
  const shareUrl = encodeURIComponent(url)
  const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`
  const xUrl = `https://twitter.com/intent/tweet?url=${shareUrl}`
  const instagramUrl = 'https://www.instagram.com'
  return (
    <div className="flex gap-2 items-center h-fit">
      <Link href={xUrl}>
        <div className="relative w-8 h-8">
          <Image src="/icons/x.svg" alt="X icon" fill className="object-contain" />
        </div>
      </Link>
      <Link href={instagramUrl}>
        <div className="relative w-8 h-8">
          <Image src="/icons/insta.svg" alt="Instagram icon" fill className="object-contain" />
        </div>
      </Link>
      <Link href={facebookUrl}>
        <div className="relative w-8 h-8">
          <Image src="/icons/facebook.svg" alt="Facebook icon" fill className="object-contain" />
        </div>
      </Link>
    </div>
  )
}

export default ShareIcons
