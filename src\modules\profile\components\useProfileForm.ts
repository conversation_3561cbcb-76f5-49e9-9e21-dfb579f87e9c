import useApi from '@/hooks/useApi'
import { IProfile } from '@/types'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'

const defaultValues: IProfile = {
  email: '',
  first_name: '',
  last_name: '',
  phone: '',
  password: '',
  password_confirmation: '',
}

const useProfileForm = () => {
  const t = useTranslations()

  const { submit, isPending } = useApi({
    path: '/contact-us',
    method: 'POST',
    handleSuccess: false,
    onSuccess: () => {
      toast.success(t('contact.send_message_success'))
    },
  })

  const handleSubmit = async (payload: IProfile) => {
    const formdata = new FormData()
    formdata.append('email', payload.email)
    formdata.append('first_name', payload.first_name)
    formdata.append('last_name', payload.last_name)
    formdata.append('phone', payload.phone)
    formdata.append('password', payload.password)
    formdata.append('password_confirmation', payload.password_confirmation)
    submit(formdata)
  }

  return {
    t,
    isPending,
    handleSubmit,
    defaultValues,
  }
}

export default useProfileForm
