import { cn } from '@/utils/cn'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import Image from 'next/image'
import { ComponentProps } from 'react'

// images
import HeroImage from '/public/images/hero_section.png'

// routes
import { Routes } from '@/routes/routes'

const HeroSection = ({ className }: { className?: ComponentProps<'header'>['className'] }) => {
  const t = useTranslations()
  return (
    <header
      className={cn(
        'container grid md:grid-cols-2 grid-cols-1 items-center justify-between gap-14 md:min-h-[581px]',
        className
      )}
    >
      {/* text  */}
      <div className="flex flex-col lg:gap-8 md:gap-6 gap-4 w-full">
        <h1 className="text-primary-02 font-bold lg:text-5xl md:text-4xl sm:text-3xl text-2xl lg:w-[72%]">
          {t('hero.title')}
        </h1>
        <p className="text-primary-03 font-normal lg:text-3xl md:text-xl text-lg">{t('hero.description')}</p>

        <div className="flex justify-start items-center gap-2 flex-wrap">
          <Link
            href={Routes.BOOKINGS}
            className="main_btn_gradient min-h-[44px] text-white md:h-[48px] lg:px-8 md:px-6 px-4 lg:py-3 py-2 rounded-3xl lg:w-[197px] w-[180px] text-center font-bold"
          >
            {t('button.book_appointment')}
          </Link>
          <Link
            href={Routes.ACTIVITIES}
            className="border-2 border-primary-02 min-h-[44px] text-primary-02 md:h-[48px] lg:px-8 md:px-6 px-4 lg:py-3 py-2 rounded-3xl text-center font-bold"
          >
            {t('button.view_latest_activities')}
          </Link>
        </div>
      </div>

      {/* image  */}
      <Image
        src={HeroImage}
        alt={t('name')}
        className="rounded-3xl w-full md:max-w-[548px] h-auto max-h-[479px] object-cover object-top"
        width={548}
        height={479}
        priority
      />
    </header>
  )
}

export default HeroSection
