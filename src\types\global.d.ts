import type { Locale } from '@/i18n-config'

interface IPageProps {
  params: Promise<{ lang: Locale }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}
interface IDDl {
  id: number
  name: string
}

interface IStatus<T = string | number> {
  value: T
  label: string
}

interface IFileResponse {
  id: number
  name: string
  path: string
  type: string
  extension: string
}

interface ErrorResponse {}
interface ApiReturn<T> {
  data?: T
}

interface ActionServiceReturn<T> {
  data?: ApiReturn<T>
  status: number | boolean
  message?: string
  error?: ErrorResponse
}

interface IPagination {
  total: number
  count: number
  per_page: number
  next_page_url: string
  prev_page_url: string
  current_page: number
  total_pages: number
}