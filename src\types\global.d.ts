import type { Locale } from '@/i18n-config'

interface IPageProps {
  params: Promise<{ lang: Locale }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}
export interface IDDl {
  id: number
  name: string
}

export interface IStatus<T = string | number> {
  value: T
  label: string
}

export interface IFileResponse {
  id: number
  name: string
  path: string
  type: string
  extension: string
}

export interface ErrorResponse {}

export interface ActionServiceReturn<T> {
  data?: T
  status: boolean
  message?: string
  error?: ErrorResponse
}
