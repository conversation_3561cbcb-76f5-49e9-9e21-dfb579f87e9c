'use server'

import { ActionServiceReturn } from '@/types'
import { apiService, ApiServiceProps } from './apiService'

export type ActionServiceProps = ApiServiceProps

export async function actionService<T>(props: ActionServiceProps, _: any, body: any): Promise<ActionServiceReturn<T>> {
  const res = await apiService({ body, ...props })

  return {
    status: res.status === 200,
    ...(res.data && { data: res.data }),
    message: res?.message || 'Success',
    error: res.status === 422 ? res.errors : res.message,
  }
}
