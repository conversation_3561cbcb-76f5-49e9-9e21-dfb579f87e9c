import { actionService } from '@/services'
import { IResetPassword } from '@/types'
import { getCookie, deleteCookie } from 'cookies-next'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'
import { toast } from 'sonner'
import { useEffect, useTransition } from 'react'
import useApi from '@/hooks/useApi'
import { Routes } from '@/routes/routes'
import useError from '@/hooks/useError'

const defaultValues: IResetPassword = {
  email: '',
  code: '',
  password: '',
  password_confirmation: '',
}

const useResetPassword = () => {
  const t = useTranslations()
  const { errorToast } = useError()
  const [isPending, startTransition] = useTransition()
  const code = getCookie('code')
  const email = getCookie('em')

  useEffect(() => {
    if (!email || !code) {
      toast.error(t('auth.session_expired'))
      redirect(Routes.AUTH.FORGOT_PASSWORD)
    }
  }, [email, code, t])

  const { action, state } = useApi({
    path: 'auth/reset-password',
    method: 'POST',
    onSuccess: (state) => {
      toast.success(state.message)
      deleteCookie('code')
      deleteCookie('em')
      redirect(Routes.AUTH.LOGIN)
    },
    onError: (error) => {
      errorToast(error)
    },
  })

  const onSubmit = async (payload: IResetPassword) => {
    if (!email || !code) {
      toast.error(t('auth.missing_required_data'))
      return
    }

    const submitData = {
      ...payload,
      email: email as string,
      code: code as string,
    }

    startTransition(() => {
      action(submitData)
    })
  }

  return {
    t,
    isPending,
    onSubmit,
    defaultValues,
  }
}

export default useResetPassword
