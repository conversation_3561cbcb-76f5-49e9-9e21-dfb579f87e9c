/* eslint-disable prettier/prettier */
import { Slot } from '@radix-ui/react-slot'
import { cva } from 'class-variance-authority'
import * as React from 'react'

import { cn } from '@/lib/utils'

import type { VariantProps } from 'class-variance-authority'

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none  aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive capitalize cursor-pointer focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]` rounded-[24px] min:h-[48px] w-[192px] font-bold text-lg cursor-pointer",
  {
    variants: {
      variant: {
        default: 'main_btn_gradient text-white',
        outline: 'text-primary-02 border border-primary-02 w-[238px]',
        secondary: 'bg-white text-primary-02',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 ',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-12 px-4 py-2 has-[>svg]:px-3',
        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',
        icon: 'size-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

export type TButtonProps = React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
    permissionName?: string
  }

function Button({
  className,
  variant,
  size,
  asChild = false,
  // permissionName, //  prevent permissionName from being passed to the DOM.
  ...props
}: TButtonProps) {
  const Comp = asChild ? Slot : 'button'

  return (
    <Comp type="button" data-slot="button" className={cn(buttonVariants({ variant, size, className }))} {...props} />
  )
}

export { Button, buttonVariants }
