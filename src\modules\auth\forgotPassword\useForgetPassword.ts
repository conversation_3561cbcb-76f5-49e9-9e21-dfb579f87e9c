'use client'

import { useTranslations } from 'next-intl'
import { IForgetPassword } from '@/types'
import { toast } from 'sonner'
import { setCookie } from 'cookies-next'
import { redirect } from 'next/navigation'
import useApi from '@/hooks/useApi'
import { Routes } from '@/routes/routes'
import { useTransition } from 'react'
import useError from '@/hooks/useError'

const defaultValues: IForgetPassword = {
  email: '',
}

const useForgetPassword = () => {
  const t = useTranslations()
  const { errorToast } = useError()
  const [isPending, startTransition] = useTransition()
  const { action, state } = useApi({
    path: 'auth/forgot-password',
    method: 'POST',
    onSuccess: (state) => {
      toast.success(state.message)
      setCookie('em', state.email)
      redirect(Routes.AUTH.VERIFY_RESET_CODE)
    },
    onError: (error) => {
      errorToast(error)
    },
  })

  const handleSubmit = (payload: IForgetPassword) => {
    startTransition(() => {
      action(payload)
    })
  }

  return {
    t,
    isPending,
    handleSubmit,
    defaultValues,
  }
}

export default useForgetPassword
